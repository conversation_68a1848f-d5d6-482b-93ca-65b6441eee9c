export interface InventoryItem {
  id: number;
  name: string;
  quantity_available: number;
  reorder_level: number;
  last_updated: string; // ISO date string
  expiry_date: string | null; // nullable date
  product: string;
  store: string;
  branch: string;
  product_name: string | null;
  store_name: string | null;
  branch_name: string | null;
}

// API response structure
export interface InventoryApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: InventoryItem[];
}

export interface CreateInventoryItemRequest {
  quantity_available: number;
  reorder_level: number;
  expiry_date?: string | null;
  product: string;
  store: string;
  branch: string;
}