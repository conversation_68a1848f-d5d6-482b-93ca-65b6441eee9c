export interface IU_TaxType {
    id: number;
    name: string;
    category: 'vat' | 'wht';
    rate: string | null;
    is_active: boolean | null;
}

export interface IU_Supplier {
    id: number;
    name: string;
    code: string;
}

export interface IU_Store {
    id: number;
    name: string;
    code: string;
}

// API Response interfaces
export interface IU_TaxTypeResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: IU_TaxType[];
}

export interface IU_SupplierResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: IU_Supplier[];
}

export interface IU_StoreResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: IU_Store[];
}

export interface IU_Branch {
    id: number;
    name: string;
    code: string;
    location?: string;
}

export interface IU_Menu {
    id: number;
    name: string | null;
    price: string | null;
    description: string | null;
}

export interface IU_BranchResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: IU_Branch[];
}

export interface IU_MenuResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: IU_Menu[];
}