import React, { useState, useEffect, useMemo } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  useCreateRecipeMutation,
  useUpdateRecipeMutation
} from '@/redux/slices/recipe-data';
import { useGetRecipeGroupQuery } from '@/redux/slices/recipe-group';
import { useGetInventoryItemQuery } from '@/redux/slices/inventory';
import { useCreateRecipeVersionMutation } from '@/redux/slices/recipe-version';
import { useGetRecipeQuery } from '@/redux/slices/recipe-data';
import {
  useGetInventoryUtilityBranchQuery,
  useGetInventoryUtilityMenusQuery
} from '@/redux/slices/inventory-utilities';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableRow
} from '@/components/ui/table';
import {
  X,
  Save,
  Plus,
  Trash2,
  ChefHat,
  Search,
  GitBranch,
  Package,
  Clock,
  FileText,
  Utensils,
  Camera,
  Upload,
  Image as ImageIcon
} from 'lucide-react';
import { Recipe } from '../types/recipe-type';


const recipeSchema = z.object({
  name: z.string().min(1, 'Recipe name is required').max(100, 'Name must be less than 100 characters'),
  recipe_type: z.enum(['Prep', 'Serving']),
  portion_size: z.string().min(1, 'Portion size is required'),
  preparation_time: z.number().min(0, 'Preparation time must be positive'),
  cooking_time: z.number().min(0, 'Cooking time must be positive'),
  instructions: z.string().min(1, 'Instructions are required'),
  tools_required: z.string().nullable(),
  dietary_flags: z.array(z.string()).optional(),
  is_active: z.boolean().nullable(),
  recipe_group: z.number().min(1, 'Recipe group is required'),
  location: z.string().nullable(),
  menu_item: z.number().nullable(),
  image: z.string().nullable(),
  ingredients: z.array(z.object({
    inventory_item_id: z.number().min(1, 'Ingredient is required'),
    quantity: z.string().min(1, 'Quantity is required'),
    unit_of_measure: z.number().min(1, 'Unit of measure is required'),
    cost_per_unit: z.string().optional(),
    total_cost: z.string().optional(),
  })).min(1, 'At least one ingredient is required'),
  labor_cost: z.string().optional(),
  packaging_cost: z.string().optional(),
  target_food_cost_percentage: z.number().min(0).max(100).optional(),
});

type RecipeFormData = z.infer<typeof recipeSchema>;

interface RecipeFormProps {
  item?: Recipe;
  onClose: () => void;
  onSuccess: () => void;
}

const DIETARY_FLAGS = [
  'Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Nut-Free',
  'Halal', 'Kosher', 'Low-Carb', 'Keto', 'Paleo'
];

const ALLERGEN_FLAGS = [
  'Contains Nuts', 'Contains Dairy', 'Contains Gluten', 'Contains Eggs',
  'Contains Soy', 'Contains Fish', 'Contains Shellfish', 'Contains Sesame',
  'Contains Sulfites', 'Contains Mustard'
];

const COMMON_TOOLS = [
  'Mixer', 'Oven', 'Blender', 'Food Processor', 'Whisk', 'Knife',
  'Cutting Board', 'Saucepan', 'Frying Pan', 'Baking Sheet',
  'Measuring Cups', 'Measuring Spoons', 'Stand Mixer', 'Grill',
  'Steamer', 'Microwave', 'Thermometer', 'Scale'
];

export const RecipeForm: React.FC<RecipeFormProps> = ({
  item,
  onClose,
  onSuccess
}) => {
  const [createRecipe, { isLoading: isCreating }] = useCreateRecipeMutation();
  const [updateRecipe, { isLoading: isUpdating }] = useUpdateRecipeMutation();
  const [createVersion] = useCreateRecipeVersionMutation();
  const { data: groupsData } = useGetRecipeGroupQuery({ params: {} });
  const { data: inventoryData } = useGetInventoryItemQuery({ params: {} });
  const { data: recipesData } = useGetRecipeQuery({ params: {} });
  const { data: branchesData } = useGetInventoryUtilityBranchQuery({ params: {} });
  const { data: menusData } = useGetInventoryUtilityMenusQuery({ params: {} });

  const [selectedDietaryFlags, setSelectedDietaryFlags] = useState<string[]>([]);
  const [selectedAllergenFlags, setSelectedAllergenFlags] = useState<string[]>([]);
  const [customDietaryFlag, setCustomDietaryFlag] = useState<string>('');
  const [customAllergenFlag, setCustomAllergenFlag] = useState<string>('');
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [ingredientSearchTerm, setIngredientSearchTerm] = useState<string>('');
  const [customTool, setCustomTool] = useState<string>('');
  const [showIngredientModal, setShowIngredientModal] = useState(false);
  const [modalSearchTerm, setModalSearchTerm] = useState('');

  const isEditing = !!item;
  const groups = groupsData?.results || [];
  const inventoryItems = inventoryData?.results || [];
  const availableRecipes = recipesData?.results || [];
  const branches = branchesData?.results || [];
  const menus = menusData?.results || [];

  // Combine inventory items and recipes for ingredient selection
  const availableIngredients = useMemo(() => {
    const inventory = inventoryItems.map(item => ({
      id: item.id,
      name: `${item.product}`,
      type: 'inventory' as const,
      store: 0, // This should come from product data
      branch: 'unit' // This should come from product data
    }));

    const recipes = availableRecipes
      .filter(recipe => recipe.id !== item?.id) // Don't include current recipe
      .map(recipe => ({
        id: recipe.id,
        name: `${recipe.name} (Recipe)`,
        type: 'recipe' as const,
        store: 0, // Calculate from recipe cost
        branch: recipe.portion_size
      }));

    return [...inventory, ...recipes];
  }, [inventoryItems, availableRecipes, item?.id]);

  // Filter ingredients based on search term
  const filteredIngredients = useMemo(() => {
    if (!ingredientSearchTerm) return availableIngredients;
    return availableIngredients.filter(ingredient =>
      ingredient.name.toLowerCase().includes(ingredientSearchTerm.toLowerCase())
    );
  }, [availableIngredients, ingredientSearchTerm]);

  // Filter ingredients for modal
  const modalFilteredIngredients = useMemo(() => {
    if (!modalSearchTerm) return availableIngredients;
    return availableIngredients.filter(ingredient =>
      ingredient.name.toLowerCase().includes(modalSearchTerm.toLowerCase())
    );
  }, [availableIngredients, modalSearchTerm]);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm<RecipeFormData>({
    resolver: zodResolver(recipeSchema),
    defaultValues: {
      name: '',
      recipe_type: 'Prep',
      portion_size: '',
      preparation_time: 0,
      cooking_time: 0,
      instructions: '',
      tools_required: '',
      is_active: true,
      recipe_group: 0,
      location: null,
      menu_item: null,
      image: '',
      ingredients: [],
      labor_cost: '0',
      packaging_cost: '0',
      target_food_cost_percentage: 30,
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'ingredients'
  });

  const watchedIngredients = watch('ingredients');
  const laborCost = watch('labor_cost') || '0';
  const packagingCost = watch('packaging_cost') || '0';
  const targetFoodCostPercentage = watch('target_food_cost_percentage') || 30;

  // Calculate total costs
  const totalIngredientCost = useMemo(() => {
    return watchedIngredients.reduce((total, ingredient) => {
      const cost = parseFloat(ingredient.total_cost || '0');
      return total + (isNaN(cost) ? 0 : cost);
    }, 0);
  }, [watchedIngredients]);

  const totalRecipeCost = useMemo(() => {
    const labor = parseFloat(laborCost);
    const packaging = parseFloat(packagingCost);
    return totalIngredientCost + (isNaN(labor) ? 0 : labor) + (isNaN(packaging) ? 0 : packaging);
  }, [totalIngredientCost, laborCost, packagingCost]);

  const recommendedSellingPrice = useMemo(() => {
    if (targetFoodCostPercentage > 0) {
      return totalRecipeCost / (targetFoodCostPercentage / 100);
    }
    return 0;
  }, [totalRecipeCost, targetFoodCostPercentage]);

  // Helper functions
  // const addIngredient = () => {
  //   append({
  //     inventory_item_id: 0,
  //     quantity: '',
  //     unit_of_measure: 0,
  //     cost_per_unit: '0',
  //     total_cost: '0',
  //   });
  // };

  // const calculateIngredientCost = (index: number, quantity: string, costPerUnit: string) => {
  //   const qty = parseFloat(quantity) || 0;
  //   const cost = parseFloat(costPerUnit) || 0;
  //   const totalCost = (qty * cost).toFixed(2);
  //   setValue(`ingredients.${index}.total_cost`, totalCost);
  // };

  // const handleIngredientSelect = (index: number, ingredientId: string) => {
  //   const ingredient = availableIngredients.find(ing => ing.id.toString() === ingredientId);
  //   if (ingredient) {
  //     setValue(`ingredients.${index}.inventory_item_id`, ingredient.id);
  //     setValue(`ingredients.${index}.unit_of_measure`, 1); // Default unit ID
  //     setValue(`ingredients.${index}.cost_per_unit`, ingredient.cost_per_unit.toString());

  //     // Recalculate cost if quantity exists
  //     const currentQuantity = watch(`ingredients.${index}.quantity`);
  //     if (currentQuantity) {
  //       calculateIngredientCost(index, currentQuantity, ingredient.cost_per_unit.toString());
  //     }
  //   }
  // };

  // const handleDietaryFlagToggle = (flag: string) => {
  //   const newFlags = selectedDietaryFlags.includes(flag)
  //     ? selectedDietaryFlags.filter(f => f !== flag)
  //     : [...selectedDietaryFlags, flag];
  //   setSelectedDietaryFlags(newFlags);
  //   setValue('dietary_flags', newFlags);
  // };

  const handleAllergenFlagToggle = (flag: string) => {
    const newFlags = selectedAllergenFlags.includes(flag)
      ? selectedAllergenFlags.filter(f => f !== flag)
      : [...selectedAllergenFlags, flag];
    setSelectedAllergenFlags(newFlags);
  };

  const handleToolToggle = (tool: string) => {
    const newTools = selectedTools.includes(tool)
      ? selectedTools.filter(t => t !== tool)
      : [...selectedTools, tool];
    setSelectedTools(newTools);
    setValue('tools_required', newTools.join(', '));
  };

  const handleAddCustomTool = () => {
    if (customTool.trim() && !selectedTools.includes(customTool.trim())) {
      const newTools = [...selectedTools, customTool.trim()];
      setSelectedTools(newTools);
      setValue('tools_required', newTools.join(', '));
      setCustomTool('');
    }
  };

  const handleRemoveTool = (tool: string) => {
    const newTools = selectedTools.filter(t => t !== tool);
    setSelectedTools(newTools);
    setValue('tools_required', newTools.join(', '));
  };

  // const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0];
  //   if (file) {
  //     const reader = new FileReader();
  //     reader.onload = () => {
  //       const result = reader.result as string;
  //       setImagePreview(result);
  //       setValue('image', result);
  //     };
  //     reader.readAsDataURL(file);
  //   }
  // };

  // const handleCreateNewVersion = async () => {
  //   if (!item) return;

  //   try {
  //     const formData = watch();
  //     await createVersion({
  //       recipe_id: item.id,
  //       version_data: formData,
  //       version_notes: 'New version created from form'
  //     });
  //     onSuccess();
  //   } catch (error) {
  //     console.error('Failed to create new version:', error);
  //   }
  // };

  useEffect(() => {
    if (item) {
      reset({
        name: item.name,
        recipe_type: item.recipe_type,
        portion_size: item.portion_size,
        preparation_time: item.preparation_time,
        cooking_time: item.cooking_time,
        instructions: item.instructions,
        tools_required: item.tools_required || '',
        is_active: item.is_active ?? true,
        recipe_group: item.recipe_group,
        location: item.location || null,
        menu_item: item.menu_item,
        image: item.image || '',
        ingredients: [],
        labor_cost: '0',
        packaging_cost: '0',
        target_food_cost_percentage: 30,
      });
      setSelectedDietaryFlags(item.dietary_flags || []);
      setImagePreview(item.image);
    }
  }, [item, reset]);

  const addIngredient = () => {
    setShowIngredientModal(true);
  };

  const addIngredientFromModal = (ingredient: any) => {
    append({
      inventory_item_id: ingredient.id,
      quantity: '',
      unit_of_measure: 1, // Default unit ID
      cost_per_unit: '',
      total_cost: '0',
    });
    setShowIngredientModal(false);
    setModalSearchTerm('');
  };

  const calculateIngredientCost = (index: number, quantity: string, costPerUnit: string) => {
    const qty = parseFloat(quantity);
    const cost = parseFloat(costPerUnit);
    const total = (isNaN(qty) || isNaN(cost)) ? 0 : qty * cost;
    setValue(`ingredients.${index}.total_cost`, total.toFixed(2));
  };

  const handleIngredientSelect = (index: number, ingredientId: string) => {
    const ingredient = availableIngredients.find(ing => ing.id.toString() === ingredientId);
    if (ingredient) {
      setValue(`ingredients.${index}.inventory_item_id`, ingredient.id);
      // Auto-fill unit of measure and cost per unit
      setValue(`ingredients.${index}.unit_of_measure`, 1); // Default unit ID
      // setValue(`ingredients.${index}.cost_per_unit`, ingredient.cost_per_unit.toString());

      // Recalculate cost if quantity is already set
      const currentQuantity = watch(`ingredients.${index}.quantity`);
      if (currentQuantity) {
        // calculateIngredientCost(index, currentQuantity, ingredient.cost_per_unit.toString());
      }
    }
  };

  const handleCreateNewVersion = async () => {
    if (!item) return;

    try {
      const currentFormData = watch();
      await createVersion({
        recipe: item.id.toString(),
        version_number: `v${Date.now()}`,
        data_snapshot: {
          ...item,
          ...currentFormData,
          dietary_flags: selectedDietaryFlags
        },
      }).unwrap();

      alert('New version created successfully!');
    } catch (error) {
      console.error('Failed to create new version:', error);
      alert('Failed to create new version');
    }
  };

  const handleDietaryFlagToggle = (flag: string) => {
    setSelectedDietaryFlags(prev =>
      prev.includes(flag)
        ? prev.filter(f => f !== flag)
        : [...prev, flag]
    );
  };

  const handleAddCustomDietaryFlag = () => {
    if (customDietaryFlag.trim() && !selectedDietaryFlags.includes(customDietaryFlag.trim())) {
      setSelectedDietaryFlags(prev => [...prev, customDietaryFlag.trim()]);
      setCustomDietaryFlag('');
    }
  };

  const handleAddCustomAllergenFlag = () => {
    if (customAllergenFlag.trim() && !selectedAllergenFlags.includes(customAllergenFlag.trim())) {
      setSelectedAllergenFlags(prev => [...prev, customAllergenFlag.trim()]);
      setCustomAllergenFlag('');
    }
  };

  const handleRemoveCustomFlag = (flag: string, type: 'dietary' | 'allergen') => {
    if (type === 'dietary') {
      setSelectedDietaryFlags(prev => prev.filter(f => f !== flag));
    } else {
      setSelectedAllergenFlags(prev => prev.filter(f => f !== flag));
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        setValue('image', result);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: RecipeFormData) => {
    try {
      const recipeData = {
        ...data,
        dietary_flags: selectedDietaryFlags,
        preparation_time: Number(data.preparation_time),
        cooking_time: Number(data.cooking_time),
      };

      if (isEditing && item) {
        // Create a new version before updating
        await createVersion({
          recipe: item.id.toString(),
          version_number: `v${Date.now()}`,
          data_snapshot: item,
        }).unwrap();

        await updateRecipe({
          id: item.id,
          body: recipeData
        }).unwrap();
      } else {
        await createRecipe(recipeData).unwrap();
      }

      onSuccess();
    } catch (error) {
      console.error('Failed to save recipe:', error);
    }
  };

  const getInventoryItemName = (id: number) => {
    const item = inventoryItems.find(inv => inv.id === id);
    return item ? `${item.product}` : 'Unknown Item';
  };

  return (
    <div className="border p-6 rounded-lg space-y-6">
      <div>
        <div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 font-bold">
              <ChefHat className="h-6 w-6" />
              {isEditing ? 'Edit Recipe' : 'Add New Recipe'}
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Recipe Name *</label>
                <Input
                  {...register('name')}
                  placeholder="Enter recipe name"
                  className='focus-visible:ring-0'
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Recipe Type *</label>
                <Select
                  value={watch('recipe_type')}
                  onValueChange={(value: 'Prep' | 'Serving') => setValue('recipe_type', value)}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue placeholder="Select recipe type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Prep">Prep Recipe</SelectItem>
                    <SelectItem value="Serving">Final Recipe</SelectItem>
                  </SelectContent>
                </Select>
                {errors.recipe_type && (
                  <p className="text-sm text-red-600">{errors.recipe_type.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Recipe Group *</label>
                <Select
                  value={watch('recipe_group')?.toString()}
                  onValueChange={(value) => setValue('recipe_group', parseInt(value))}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue placeholder="Select recipe group" />
                  </SelectTrigger>
                  <SelectContent>
                    {groups.map(group => (
                      <SelectItem key={group.id} value={group.id.toString()}>
                        {group.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.recipe_group && (
                  <p className="text-sm text-red-600">{errors.recipe_group.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Portion Size *</label>
                <Input
                  {...register('portion_size')}
                  placeholder="e.g., 1 serving, 500g"
                  className='focus-visible:ring-0'
                />
                {errors.portion_size && (
                  <p className="text-sm text-red-600">{errors.portion_size.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Preparation Time (minutes)
                </label>
                <Input
                  type="number"
                  {...register('preparation_time', { valueAsNumber: true })}
                  placeholder="0"
                  min="0"
                  step="1"
                  className='focus-visible:ring-0'
                />
                {errors.preparation_time && (
                  <p className="text-sm text-red-600">{errors.preparation_time.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Cooking Time (minutes)
                </label>
                <Input
                  type="number"
                  {...register('cooking_time', { valueAsNumber: true })}
                  placeholder="0"
                  min="0"
                  step="1"
                  className='focus-visible:ring-0'
                />
                {errors.cooking_time && (
                  <p className="text-sm text-red-600">{errors.cooking_time.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Location (Branch)</label>
                <Select
                  value={watch('location') || ''}
                  onValueChange={(value) => setValue('location', value)}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue placeholder="Select branch location" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.length === 0 ? (
                      <SelectItem value="Not Available" disabled>
                        Branches not available
                      </SelectItem>
                    ) : (
                      branches.map(branch => (
                        <SelectItem key={branch.id} value={branch.id.toString()}>
                          {branch.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Menu Item</label>
                <Select
                  value={watch('menu_item')?.toString() || ''}
                  onValueChange={(value) => setValue('menu_item', value ? parseInt(value) : null)}
                >
                  <SelectTrigger className='focus:ring-0'>
                    <SelectValue placeholder="Select menu item" />
                  </SelectTrigger>
                  <SelectContent>
                    {menus.length === 0 ? (
                      <SelectItem value="Not Available" disabled>
                        Menu items not available
                      </SelectItem>
                    ) : (
                      menus.map(menu => (
                        <SelectItem key={menu.id} value={menu.id.toString()}>
                          {menu.name || `Menu Item ${menu.id}`}
                          {menu.price && ` - Ksh ${menu.price}`}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Preparation & Metadata Section */}
            <div>
              <div className="space-y-6">
                {/* Image Upload */}
                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Camera className="h-4 w-4" />
                    Recipe Image Upload
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                    <div className="text-center">
                      {imagePreview ? (
                        <div className="space-y-4">
                          <div className="mx-auto w-32 h-32 border rounded-md overflow-hidden">
                            <img
                              src={imagePreview}
                              alt="Recipe preview"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex justify-center gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setImagePreview(null);
                                setValue('image', '');
                              }}
                            >
                              Remove Image
                            </Button>
                            <label className="cursor-pointer">
                              <Button type="button" variant="outline" size="sm" asChild>
                                <span>
                                  <Upload className="h-4 w-4 mr-2" />
                                  Change Image
                                </span>
                              </Button>
                              <Input
                                type="file"
                                accept="image/*"
                                onChange={handleImageUpload}
                                className="hidden"
                              />
                            </label>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
                          <div>
                            <label className="cursor-pointer">
                              <Button type="button" variant="outline" asChild>
                                <span>
                                  <Upload className="h-4 w-4 mr-2" />
                                  Upload Recipe Image
                                </span>
                              </Button>
                              <Input
                                type="file"
                                accept="image/*"
                                onChange={handleImageUpload}
                                className="hidden"
                              />
                            </label>
                            <p className="text-sm text-gray-500 mt-2">
                              PNG, JPG, GIF up to 10MB
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Rich Text Instructions */}
                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Preparation Instructions *
                  </label>
                  <div className="border rounded-md">
                    <textarea
                      {...register('instructions')}
                      rows={8}
                      className="w-full p-4 border-0 rounded-b-md resize-none focus:outline-none"
                      placeholder="Step 1: Prepare ingredients by...&#10;&#10;Step 2: Heat the pan to medium temperature...&#10;&#10;Step 3: Add ingredients in the following order..."
                    />
                  </div>
                  {errors.instructions && (
                    <p className="text-sm text-red-600">{errors.instructions.message}</p>
                  )}
                </div>

                {/* Tools Required with Tags */}
                <div className="space-y-3">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Utensils className="h-4 w-4" />
                    <p className='font-bold'>Tools Required (Optional)</p>
                  </label>

                  {/* Custom Tool Input */}
                  <div className="flex gap-2">
                    <Input
                      value={customTool}
                      onChange={(e) => setCustomTool(e.target.value)}
                      placeholder="Add custom tool..."
                      className='focus-visible:ring-0'
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddCustomTool();
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleAddCustomTool}
                      disabled={!customTool.trim()}
                      className='py-[20px]'
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Common Tools Selection */}
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600">Select common tools:</p>
                    <div className="flex flex-wrap gap-2">
                      {COMMON_TOOLS.map(tool => (
                        <Badge
                          key={tool}
                          variant={selectedTools.includes(tool) ? 'default' : 'outline'}
                          className="cursor-pointer px-5 py-1.5"
                          onClick={() => handleToolToggle(tool)}
                        >
                          {tool}
                        </Badge>
                      ))}
                    </div>
                  </div>


                  {/* Selected Tools Display */}
                  {selectedTools.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Selected tools:</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedTools.map(tool => (
                          <Badge
                            key={tool}
                            variant="secondary"
                            className="flex items-center gap-1 px-5 py-1.5"
                          >
                            {tool}
                            <X
                              className="h-3 w-3 cursor-pointer hover:text-red-500"
                              onClick={() => handleRemoveTool(tool)}
                            />
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Hidden input for form submission */}
                  <Input
                    {...register('tools_required')}
                    type="hidden"
                  />
                </div>
              </div>
            </div>

            {/* Dietary & Allergen Information */}
            <div>
              <div className="space-y-6">
                {/* Dietary Flags */}
                <div className="space-y-3">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <p className='font-bold'>Dietary Flags</p>
                  </label>
                  <p className="text-sm text-gray-600">
                    Select all dietary categories that apply to this recipe:
                  </p>

                  {/* Custom Dietary Flag Input */}
                  <div className="flex gap-2 items-center">
                    <Input
                      type="text"
                      placeholder="Add custom dietary flag..."
                      value={customDietaryFlag}
                      onChange={(e) => setCustomDietaryFlag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleAddCustomDietaryFlag()}
                      className="flex-1 focus-visible:ring-0"
                    />
                    <Button
                      type="button"
                      onClick={handleAddCustomDietaryFlag}
                      disabled={!customDietaryFlag.trim()}
                      size="sm"
                      className="py-[20px]"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Predefined Dietary Flags */}
                  <div className="flex flex-wrap gap-2">
                    {DIETARY_FLAGS.map(flag => (
                      <Badge
                        key={flag}
                        variant={selectedDietaryFlags.includes(flag) ? 'default' : 'outline'}
                        className="cursor-pointer px-5 py-1.5 text-center"
                        onClick={() => handleDietaryFlagToggle(flag)}
                      >
                        {flag}
                      </Badge>
                    ))}
                  </div>


                  {/* Selected Custom Dietary Flags */}
                  {selectedDietaryFlags.filter(flag => !DIETARY_FLAGS.includes(flag)).length > 0 && (
                    <div className="space-y-2">
                      <p className="text-xs text-gray-500">Selected dietary flags:</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedDietaryFlags.map(flag => (
                          <Badge
                            key={flag}
                            variant="secondary"
                            className="cursor-pointer px-5 py-1.5 "
                          >
                            {flag}
                            <X
                              className="h-3 w-3 ml-1 cursor-pointer"
                              onClick={() => handleRemoveCustomFlag(flag, 'dietary')}
                            />
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Allergen Information */}
                <div className="space-y-3">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <p className='font-bold'>Allergen Information</p>
                  </label>

                  {/* Custom Allergen Flag Input */}
                  <div className="flex gap-2 items-center">
                    <Input
                      type="text"
                      placeholder="Add custom allergen..."
                      value={customAllergenFlag}
                      onChange={(e) => setCustomAllergenFlag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleAddCustomAllergenFlag()}
                      className="flex-1 focus-visible:ring-0"
                    />
                    <Button
                      type="button"
                      onClick={handleAddCustomAllergenFlag}
                      disabled={!customAllergenFlag.trim()}
                      size="sm"
                      className="py-[20px]"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {ALLERGEN_FLAGS.map(flag => (
                      <Badge
                        key={flag}
                        variant={selectedAllergenFlags.includes(flag) ? 'default' : 'outline'}
                        className="cursor-pointer px-5 py-1.5 text-center"
                        onClick={() => handleAllergenFlagToggle(flag)}
                      >
                        {flag}
                      </Badge>
                    ))}
                  </div>

                  {selectedAllergenFlags.filter(flag => !DIETARY_FLAGS.includes(flag)).length > 0 && (
                    <div className="space-y-2">
                      <p className="text-xs text-gray-500">Selected allergens:</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedAllergenFlags.map(flag => (
                          <Badge
                            key={flag}
                            variant="secondary"
                            className="cursor-pointer px-5 py-1.5 "
                          >
                            {flag}
                            <X
                              className="h-3 w-3 ml-1 cursor-pointer"
                              onClick={() => handleRemoveCustomFlag(flag, 'allergen')}
                            />
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Selected Custom Allergen Flags */}
                  {selectedAllergenFlags.filter(flag => !ALLERGEN_FLAGS.includes(flag)).length > 0 && (
                    <div className="space-y-2">
                      <p className="text-xs text-gray-500">Custom allergens:</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {selectedAllergenFlags
                          .filter(flag => !ALLERGEN_FLAGS.includes(flag))
                          .map(flag => (
                            <div
                              key={flag}
                              className="flex items-center justify-between space-x-2 p-3 border border-red-500 bg-red-50 rounded-md"
                            >
                              <div className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  checked={true}
                                  readOnly
                                  className="w-4 h-4 text-red-600 rounded"
                                />
                                <span className="text-sm font-medium">{flag}</span>
                              </div>
                              <X
                                className="h-4 w-4 cursor-pointer text-red-600 hover:text-red-800"
                                onClick={() => handleRemoveCustomFlag(flag, 'allergen')}
                              />
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>

              </div>
            </div>

            {/* Ingredients Section */}
            <div>
              <div>
                <div className="flex items-center justify-between">
                  <p className='font-bold'>Ingredients</p>
                  <Button type="button" onClick={addIngredient} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Ingredient
                  </Button>
                </div>
              </div>
              <div>
                {fields.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No ingredients added yet. Click "Add Ingredient" to start.
                  </div>
                ) : (
                  <Table>
                    {/* <TableHeader>
                      <TableRow>
                        <TableHead>Ingredient</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit</TableHead>
                        <TableHead>Cost/Unit</TableHead>
                        <TableHead>Total Cost</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader> */}
                    <TableBody>
                      {fields.map((field, index) => (
                        <TableRow key={field.id} className='hover:bg-muted/0 !pb-2'>
                          <TableCell width='20%' className='!py-4'>
                            <div className="font-bold">
                              {getInventoryItemName(watch(`ingredients.${index}.inventory_item_id`))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <label className='text-xs'>Quantity</label>
                            <Input
                              {...register(`ingredients.${index}.quantity`)}
                              placeholder="0"
                              className='focus-visible:ring-0'
                              onChange={(e) => {
                                const quantity = e.target.value;
                                const costPerUnit = watch(`ingredients.${index}.cost_per_unit`) || '0';
                                calculateIngredientCost(index, quantity, costPerUnit);
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <label className='text-xs'>Unit of Measure</label>
                            <Input
                              {...register(`ingredients.${index}.unit_of_measure`, { valueAsNumber: true })}
                              placeholder="Unit (auto-filled)"
                              className="focus-visible:ring-0"
                            />
                          </TableCell>
                          <TableCell>
                            <label className='text-xs'>Cost Per Unit</label>
                            <Input
                              {...register(`ingredients.${index}.cost_per_unit`)}
                              placeholder="0.00"
                              className="focus-visible:ring-0"
                              onChange={(e) => {
                                const costPerUnit = e.target.value;
                                const quantity = watch(`ingredients.${index}.quantity`) || '0';
                                calculateIngredientCost(index, quantity, costPerUnit);
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <label className='text-xs'>Sub Total</label>
                            <Input
                              {...register(`ingredients.${index}.total_cost`)}
                              readOnly
                              className="bg-muted focus-visible:ring-0"
                            />
                          </TableCell>
                          <TableCell>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => remove(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            </div>

            {/* Costing Section */}
            <div>
              <div className="space-y-4">
                {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Labor Cost</label>
                    <Input
                      {...register('labor_cost')}
                      placeholder="0.00"
                      type="number"
                      step="0.01"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Packaging Cost</label>
                    <Input
                      {...register('packaging_cost')}
                      placeholder="0.00"
                      type="number"
                      step="0.01"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target Food Cost %</label>
                    <Input
                      {...register('target_food_cost_percentage', { valueAsNumber: true })}
                      placeholder="30"
                      type="number"
                      min="1"
                      max="100"
                    />
                  </div>
                </div> */}

                <div className="bg-muted p-7 rounded-md space-y-2">
                  <div className="flex justify-between font-semibold">
                    <span>Total Recipe Cost:</span>
                    <span>${totalRecipeCost.toFixed(2)}</span>
                  </div>
                  {/* <div className="flex justify-between text-green-600 font-semibold">
                    <span>Recommended Selling Price:</span>
                    <span>${recommendedSellingPrice.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Target Food Cost:</span>
                    <span>{targetFoodCostPercentage}%</span>
                  </div> */}
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end gap-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreating || isUpdating}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isCreating || isUpdating ? 'Saving...' : 'Save Recipe'}
              </Button>
              {isEditing && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleCreateNewVersion}
                  className="flex items-center gap-2"
                >
                  <GitBranch className="h-4 w-4" />
                  Create New Version
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>

      {/* Ingredient Selection Modal */}
      {showIngredientModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="w-full rounded-lg  max-w-4xl max-h-[80vh] overflow-hidden">
            <div className='bg-background px-6 pt-6 space-y-2'>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <p className='font-bold'>Add Ingredients</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowIngredientModal(false);
                    setModalSearchTerm('');
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search inventory items..."
                  value={modalSearchTerm}
                  onChange={(e) => setModalSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="bg-background px-6 pt-3 pb-6 overflow-y-auto max-h-[60vh]">
              {modalFilteredIngredients.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No inventory items found</p>
                  <p className="text-sm">Try adjusting your search terms</p>
                </div>
              ) : (
                <div className="flex flex-col divide-y">
                  {modalFilteredIngredients.map(ingredient => (
                    <div
                      key={`${ingredient.type}-${ingredient.id}`}
                      className="cursor-pointer"
                      onClick={() => addIngredientFromModal(ingredient)}
                    >
                      <div className="flex items-center justify-between px-3 py-2">
                        <div>
                          <h3 className="font-semibold text-sm mb-1">{ingredient.name}</h3>
                          <div className="flex text-xs text-muted-foreground">
                            <p>Store: {ingredient.store}</p>
                          </div>
                        </div>
                        <Button size="sm" variant="ghost">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};