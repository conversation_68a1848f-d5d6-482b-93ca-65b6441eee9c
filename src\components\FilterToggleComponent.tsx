import React from 'react';
import { FilterStats } from '@/hooks/useClientSideFiltering';

interface FilterToggleComponentProps {
  useClientSideFiltering: boolean;
  onToggle: () => void;
  filterStats: FilterStats;
  className?: string;
}

/**
 * Component for displaying filter toggle and statistics
 */
export const FilterToggleComponent: React.FC<FilterToggleComponentProps> = ({ 
  useClientSideFiltering, 
  onToggle, 
  filterStats,
  className = ""
}) => {
  return (
    <div className={`flex items-center gap-2 text-xs ${className}`}>
      <button
        onClick={onToggle}
        className="px-2 py-1 rounded bg-gray-100 hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
        type="button"
        aria-label={`Switch to ${useClientSideFiltering ? 'server-side' : 'client-side'} filtering`}
      >
        {useClientSideFiltering ? "🔧 Client-side" : "🌐 Server-side"} filtering
      </button>
      
      {filterStats.hasActiveFilters && (
        <span className="text-blue-600 font-medium">
          {filterStats.filteredCount} of {filterStats.originalCount} results
          {filterStats.isFiltered && useClientSideFiltering && (
            <span className="text-orange-600 ml-1">
              (client-filtered)
            </span>
          )}
        </span>
      )}
      
      {filterStats.hasActiveFilters && filterStats.isFiltered && useClientSideFiltering && (
        <div className="text-amber-600 bg-amber-50 px-2 py-1 rounded border border-amber-200">
          ⚠️ Using client-side filtering
        </div>
      )}
    </div>
  );
};

export default FilterToggleComponent;
