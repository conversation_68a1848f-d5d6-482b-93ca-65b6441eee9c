export function formatShortDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric', year: 'numeric' };
    // e.g., "Jul 27, 2023"
    const formatted = date.toLocaleDateString('en-US', options);
    // Remove comma for "Jul 27 2023"
    return formatted.replace(',', '');
}