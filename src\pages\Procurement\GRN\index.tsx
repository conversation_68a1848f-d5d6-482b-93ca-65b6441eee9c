import { Screen } from "@/app-components/layout/screen";
import Add<PERSON><PERSON> from "./modals/AddGRN";
import <PERSON><PERSON><PERSON> from "./modals/EditGRN";
import { useState, useEffect, useCallback } from "react";
import {
  useGetGRNsQuery,
  useDeleteGRNMutation,
} from "@/redux/slices/procurement";
import { useClientSideFiltering, buildApiParams, FilterStats } from "@/hooks/useClientSideFiltering";

// Inline FilterToggleComponent as fallback
const FilterToggleComponent = ({
  useClientSideFiltering,
  onToggle,
  filterStats,
  className = ""
}: {
  useClientSideFiltering: boolean;
  onToggle: () => void;
  filterStats: FilterStats;
  className?: string;
}) => {
  return (
    <div className={`flex items-center gap-2 text-xs ${className}`}>
      <button
        onClick={onToggle}
        className="px-2 py-1 rounded bg-gray-100 hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
        type="button"
      >
        {useClientSideFiltering ? "🔧 Client-side" : "🌐 Server-side"} filtering
      </button>

      {filterStats.hasActiveFilters && (
        <span className="text-blue-600 font-medium">
          {filterStats.filteredCount} of {filterStats.originalCount} results
          {filterStats.isFiltered && useClientSideFiltering && (
            <span className="text-orange-600 ml-1">
              (client-filtered)
            </span>
          )}
        </span>
      )}

      {filterStats.hasActiveFilters && filterStats.isFiltered && useClientSideFiltering && (
        <div className="text-amber-600 bg-amber-50 px-2 py-1 rounded border border-amber-200">
          ⚠️ Using client-side filtering
        </div>
      )}
    </div>
  );
};
import { ColumnDef } from "@tanstack/react-table";
import { GRN } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Package,
  Calendar,
  User,
  Building,
  FileText,
  Loader2,
  Search,
  Filter,
  Download,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const GRNIndex = () => {
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedGRN, setSelectedGRN] = useState<GRN | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [grnToDelete, setGrnToDelete] = useState<GRN | null>(null);
  
  // Search and filter states
  const [searchInput, setSearchInput] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchTerm(searchInput);
      setCurrentPage(1);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchInput]);

  // Client-side filtering setup
  const [clientSideFilteringEnabled, setClientSideFilteringEnabled] = useState(true);

  const filters = { status: statusFilter !== "all" ? statusFilter : "" };

  const apiParams = buildApiParams(
    {
      page: currentPage,
      page_size: pageSize,
      search: searchTerm,
    },
    filters,
    clientSideFilteringEnabled
  );

  // API hooks
  const {
    data: grnsData,
    isLoading,
    error,
    refetch
  } = useGetGRNsQuery(apiParams);

  // Client-side filtering
  const {
    filteredData,
    filterStats,
    useClientSideFiltering: isClientSideFiltering,
    setUseClientSideFiltering
  } = useClientSideFiltering(
    grnsData?.results,
    filters,
    searchTerm,
    {
      searchFields: ['grn_number', 'supplier_name', 'created_by_name'],
      enableClientSideFiltering: clientSideFilteringEnabled
    }
  );

  const [deleteGRN, { isLoading: deleting }] = useDeleteGRNMutation();

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchInput(value);
  };

  // Handle status filter
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!grnToDelete) return;

    try {
      await deleteGRN(grnToDelete.id!).unwrap();
      toast.success("GRN deleted successfully");
      setDeleteDialogOpen(false);
      setGrnToDelete(null);
      refetch();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to delete GRN");
    }
  };

  // Handle edit
  const handleEdit = (grn: GRN) => {
    setSelectedGRN(grn);
    setEditModalOpen(true);
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case "Full":
          return "bg-green-100 text-green-800 border-green-200";
        case "Partial":
          return "bg-yellow-100 text-yellow-800 border-yellow-200";
        case "Rejected":
          return "bg-red-100 text-red-800 border-red-200";
        default:
          return "bg-gray-100 text-gray-800 border-gray-200";
      }
    };

    return (
      <Badge className={`${getStatusColor(status)} border`}>
        {status}
      </Badge>
    );
  };

  // Table columns
  const columns: ColumnDef<GRN>[] = [
    {
      accessorKey: "grn_number",
      header: "GRN Number",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-blue-600" />
          <Link 
            to={`/procurement/grns/${row.original.id}`}
            className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
          >
            {row.getValue("grn_number")}
          </Link>
        </div>
      ),
    },
    {
      accessorKey: "purchase_order_number",
      header: "Purchase Order",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-purple-600" />
          <span className="font-medium">
            {row.original.purchase_order_number || `PO-${row.original.purchase_order}`}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "supplier_name",
      header: "Supplier",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Building className="h-4 w-4 text-gray-600" />
          <span>{row.original.supplier_name || "N/A"}</span>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => <StatusBadge status={row.getValue("status")} />,
    },
    {
      accessorKey: "received_date",
      header: "Received Date",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-600" />
          <span>
            {new Date(row.getValue("received_date")).toLocaleDateString()}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "received_by_name",
      header: "Received By",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-gray-600" />
          <span>{row.original.received_by_name || "N/A"}</span>
        </div>
      ),
    },
    {
      accessorKey: "store_name",
      header: "Store",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Building className="h-4 w-4 text-gray-600" />
          <span>{row.original.store_name || "N/A"}</span>
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link to={`/procurement/grns/${row.original.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleEdit(row.original)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => {
                setGrnToDelete(row.original);
                setDeleteDialogOpen(true);
              }}
              className="text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  if (error) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">Failed to load GRNs</p>
            <Button onClick={() => refetch()}>Try Again</Button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Goods Received Notes (GRN)
            </h1>
            <p className="text-gray-600 mt-1">
              Manage goods received from suppliers and update inventory
            </p>
            {grnsData && (
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                <span>Total: {grnsData.total_data || 0}</span>
                <span>•</span>
                <span>Page {grnsData.current_page || 1} of {grnsData.last_page || 1}</span>
              </div>
            )}
          </div>
          <Button onClick={() => setAddModalOpen(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create GRN
          </Button>
        </div>

        {/* Quick Stats */}
        {grnsData && grnsData.results && grnsData.results.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-600">
                    {grnsData.total_data || 0}
                  </p>
                  <p className="text-sm text-gray-600">Total GRNs</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">
                    {grnsData.results.filter((grn: any) => grn.status === "Full").length}
                  </p>
                  <p className="text-sm text-gray-600">Fully Received</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-yellow-600">
                    {grnsData.results.filter((grn: any) => grn.status === "Partial").length}
                  </p>
                  <p className="text-sm text-gray-600">Partial</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-red-100 rounded-lg">
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-600">
                    {grnsData.results.filter((grn: any) => grn.status === "Rejected").length}
                  </p>
                  <p className="text-sm text-gray-600">Rejected</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters and Search */}
        <div className="flex items-center gap-4 bg-white p-4 rounded-lg border">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by GRN number, PO number, or supplier..."
                className="pl-10"
                value={searchInput}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-600" />
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Full">Full</SelectItem>
                <SelectItem value="Partial">Partial</SelectItem>
                <SelectItem value="Rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>

        {/* Data Table */}
        <div className="bg-white rounded-lg border">
          {/* Filter Toggle */}
          <FilterToggleComponent
            useClientSideFiltering={isClientSideFiltering}
            onToggle={() => setUseClientSideFiltering(!isClientSideFiltering)}
            filterStats={filterStats}
            className="mb-4"
          />

          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={filteredData as GRN[]}
              pagination={{
                currentPage,
                totalPages: grnsData?.last_page || 1,
                pageSize,
                totalItems: grnsData?.total_data || 0,
                onPageChange: setCurrentPage,
                onPageSizeChange: setPageSize,
              }}
            />
          )}
        </div>
      </div>

      {/* Modals */}
      <AddGRN 
        open={addModalOpen} 
        onClose={() => setAddModalOpen(false)} 
      />
      
      <EditGRN 
        open={editModalOpen} 
        onClose={() => {
          setEditModalOpen(false);
          setSelectedGRN(null);
        }}
        grn={selectedGRN}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete GRN</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete GRN "{grnToDelete?.grn_number}"? 
              This action cannot be undone and may affect inventory records.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={deleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Screen>
  );
};

export default GRNIndex;
