import { useState, useMemo, useCallback } from 'react';

export interface FilterConfig {
  field: string;
  value: any;
  type?: 'exact' | 'includes' | 'date' | 'number' | 'boolean';
}

export interface ClientSideFilteringOptions {
  enableClientSideFiltering?: boolean;
  searchFields?: string[];
}

// Helper function to get nested object values (moved outside to avoid recreation)
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

/**
 * Custom hook for client-side filtering with fallback support
 * Provides consistent filtering behavior across procurement components
 */
export const useClientSideFiltering = <T extends Record<string, any>>(
  data: T[] | undefined,
  filters: Record<string, any>,
  searchTerm: string = '',
  options: ClientSideFilteringOptions = {}
) => {
  const [useClientSideFiltering, setUseClientSideFiltering] = useState(true);
  const { enableClientSideFiltering = true, searchFields = [] } = options;

  // Memoize the filtering logic for better performance
  const filteredData = useMemo(() => {
    if (!data || !enableClientSideFiltering || !useClientSideFiltering) {
      return data || [];
    }

    let filtered = [...data];

    // Apply search filter
    if (searchTerm && searchFields.length > 0) {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(item =>
        searchFields.some(field => {
          const value = getNestedValue(item, field);
          return value && String(value).toLowerCase().includes(searchLower);
        })
      );
    }

    // Apply status and other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all' && value !== '') {
        filtered = filtered.filter(item => {
          const itemValue = getNestedValue(item, key);

          // Handle different filter types
          if (typeof value === 'string' && typeof itemValue === 'string') {
            return itemValue.toLowerCase() === value.toLowerCase();
          }

          // Handle numeric comparisons
          if (typeof value === 'number' && typeof itemValue === 'number') {
            return itemValue === value;
          }

          // Handle boolean comparisons
          if (typeof value === 'boolean' && typeof itemValue === 'boolean') {
            return itemValue === value;
          }

          // Default exact match
          return itemValue === value;
        });
      }
    });

    return filtered;
  }, [data, filters, searchTerm, enableClientSideFiltering, useClientSideFiltering, searchFields]);

  // Calculate filter statistics
  const filterStats = useMemo(() => {
    const originalCount = data?.length || 0;
    const filteredCount = filteredData.length;
    const isFiltered = originalCount !== filteredCount;

    return {
      originalCount,
      filteredCount,
      isFiltered,
      hasActiveFilters: Object.values(filters).some(v => v && v !== 'all' && v !== '') || !!searchTerm
    };
  }, [data, filteredData, filters, searchTerm]);

  // Memoize the toggle function to prevent unnecessary re-renders
  const toggleFilteringMode = useCallback(() => {
    setUseClientSideFiltering(!useClientSideFiltering);
  }, [useClientSideFiltering]);

  return {
    filteredData,
    filterStats,
    useClientSideFiltering,
    setUseClientSideFiltering,
    toggleFilteringMode
  };
};

/**
 * Helper function to build API query parameters
 * Excludes filters when client-side filtering is enabled
 */
export const buildApiParams = (
  baseParams: Record<string, any>,
  filters: Record<string, any>,
  useClientSideFiltering: boolean
) => {
  const params = { ...baseParams };

  if (!useClientSideFiltering) {
    // Only add filters to API params when server-side filtering is enabled
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all' && value !== '') {
        params[key] = value;
        // Try alternative parameter names for better backend compatibility
        params[`${key}_filter`] = value;
        params[`filter_${key}`] = value;
      }
    });
  }

  return params;
};

/**
 * Type definition for filter statistics
 */
export interface FilterStats {
  originalCount: number;
  filteredCount: number;
  isFiltered: boolean;
  hasActiveFilters: boolean;
}
